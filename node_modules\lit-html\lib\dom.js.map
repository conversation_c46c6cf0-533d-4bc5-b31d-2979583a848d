{"version": 3, "file": "dom.js", "sourceRoot": "", "sources": ["../src/lib/dom.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAMH;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,OAAO,MAAM,KAAK,WAAW;IACrD,MAAM,CAAC,cAAc,IAAI,IAAI;IAC5B,MAAM,CAAC,cAAoC,CAAC,yBAAyB;QAClE,SAAS,CAAC;AAElB;;;;GAIG;AACH,MAAM,CAAC,MAAM,aAAa,GACtB,CAAC,SAAe,EACf,KAAgB,EAChB,MAAiB,IAAI,EACrB,SAAoB,IAAI,EAAQ,EAAE;IACjC,OAAO,KAAK,KAAK,GAAG,EAAE;QACpB,MAAM,CAAC,GAAG,KAAM,CAAC,WAAW,CAAC;QAC7B,SAAS,CAAC,YAAY,CAAC,KAAM,EAAE,MAAM,CAAC,CAAC;QACvC,KAAK,GAAG,CAAC,CAAC;KACX;AACH,CAAC,CAAC;AAEN;;;GAGG;AACH,MAAM,CAAC,MAAM,WAAW,GACpB,CAAC,SAAe,EAAE,KAAgB,EAAE,MAAiB,IAAI,EAAQ,EAAE;IACjE,OAAO,KAAK,KAAK,GAAG,EAAE;QACpB,MAAM,CAAC,GAAG,KAAM,CAAC,WAAW,CAAC;QAC7B,SAAS,CAAC,WAAW,CAAC,KAAM,CAAC,CAAC;QAC9B,KAAK,GAAG,CAAC,CAAC;KACX;AACH,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\ninterface MaybePolyfilledCe extends CustomElementRegistry {\n  readonly polyfillWrapFlushCallback?: object;\n}\n\n/**\n * True if the custom elements polyfill is in use.\n */\nexport const isCEPolyfill = typeof window !== 'undefined' &&\n    window.customElements != null &&\n    (window.customElements as MaybePolyfilledCe).polyfillWrapFlushCallback !==\n        undefined;\n\n/**\n * Reparents nodes, starting from `start` (inclusive) to `end` (exclusive),\n * into another container (could be the same container), before `before`. If\n * `before` is null, it appends the nodes to the container.\n */\nexport const reparentNodes =\n    (container: Node,\n     start: Node|null,\n     end: Node|null = null,\n     before: Node|null = null): void => {\n      while (start !== end) {\n        const n = start!.nextSibling;\n        container.insertBefore(start!, before);\n        start = n;\n      }\n    };\n\n/**\n * Removes nodes, starting from `start` (inclusive) to `end` (exclusive), from\n * `container`.\n */\nexport const removeNodes =\n    (container: Node, start: Node|null, end: Node|null = null): void => {\n      while (start !== end) {\n        const n = start!.nextSibling;\n        container.removeChild(start!);\n        start = n;\n      }\n    };\n"]}