{"compilerOptions": {"target": "es2017", "module": "es2015", "lib": ["es2017", "esnext.asynciterable", "dom"], "types": ["chai", "mocha", "trusted-types"], "declaration": true, "declarationMap": true, "sourceMap": true, "inlineSources": true, "outDir": "./", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true}, "include": ["src/**/*.ts"], "exclude": []}