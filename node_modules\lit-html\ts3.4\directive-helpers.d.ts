/**
 * @license
 * Copyright (c) 2021 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at
 * http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at
 * http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at
 * http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at
 * http://polymer.github.io/PATENTS.txt
 */
import { TemplateResult } from './lit-html.js';
export { isPrimitive } from './lib/parts.js';
/**
 * Tests if a value is a TemplateResult.
 */
export declare const isTemplateResult: (value: unknown) => value is TemplateResult;
//# sourceMappingURL=directive-helpers.d.ts.map
