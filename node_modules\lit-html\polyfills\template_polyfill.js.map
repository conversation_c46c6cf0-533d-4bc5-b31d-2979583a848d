{"version": 3, "file": "template_polyfill.js", "sourceRoot": "", "sources": ["../src/polyfills/template_polyfill.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAC,WAAW,EAAE,aAAa,EAAC,MAAM,eAAe,CAAC;AAEzD;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE,EAAE;IACrD,4EAA4E;IAC5E,6EAA6E;IAC7E,6EAA6E;IAC7E,yDAAyD;IACzD,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;QAC9D,OAAO;KACR;IACD,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IAC1E,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM,UAAU,GAAG;QACjB,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,IAAI;KACnB,CAAC;IAEF,MAAM,OAAO,GAAG,CAAC,QAA6B,EAAE,EAAE;QAChD,MAAM,OAAO,GAAG,UAAU,CAAC,sBAAsB,EAAE,CAAC;QACpD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAChC,OAAO,kCACF,UAAU,KACb,GAAG;oBACD,OAAO,OAAO,CAAC;gBACjB,CAAC,GACF;YACD,SAAS,kCACJ,UAAU,KACb,GAAG,EAAE,UAAS,IAAI;oBAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;oBACtB,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACzC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1C,CAAC,GACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC;IAC/D,QAAQ,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CACrD,OAAe,EAAE,OAAgC;QACnD,MAAM,EAAE,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC9D,IAAI,EAAE,CAAC,OAAO,KAAK,UAAU,EAAE;YAC7B,OAAO,CAAC,EAAyB,CAAC,CAAC;SACpC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\nimport {removeNodes, reparentNodes} from '../lib/dom.js';\n\n/**\n * A lightweight <template> polyfill that supports minimum features to cover\n * lit-html use cases. It provides an alternate route in case <template> is not\n * natively supported.\n * Please note that nested template, cloning template node and innerHTML getter\n * do NOT work with this polyfill.\n * If it can not fulfill your requirement, please consider using the full\n * polyfill: https://github.com/webcomponents/template\n */\nexport const initTemplatePolyfill = (forced = false) => {\n  // Minimal polyfills (like this one) may provide only a subset of Template's\n  // functionality. So, we explicitly check that at least content is present to\n  // prevent installing patching with multiple polyfills, which might happen if\n  // multiple versions of lit-html were included on a page.\n  if (!forced && 'content' in document.createElement('template')) {\n    return;\n  }\n  const contentDoc = document.implementation.createHTMLDocument('template');\n  const body = contentDoc.body;\n  const descriptor = {\n    enumerable: true,\n    configurable: true,\n  };\n\n  const upgrade = (template: HTMLTemplateElement) => {\n    const content = contentDoc.createDocumentFragment();\n    Object.defineProperties(template, {\n      content: {\n        ...descriptor,\n        get() {\n          return content;\n        },\n      },\n      innerHTML: {\n        ...descriptor,\n        set: function(text) {\n          body.innerHTML = text;\n          removeNodes(content, content.firstChild);\n          reparentNodes(content, body.firstChild);\n        },\n      },\n    });\n  };\n\n  const capturedCreateElement = Document.prototype.createElement;\n  Document.prototype.createElement = function createElement(\n      tagName: string, options?: ElementCreationOptions) {\n    const el = capturedCreateElement.call(this, tagName, options);\n    if (el.tagName === 'TEMPLATE') {\n      upgrade(el as HTMLTemplateElement);\n    }\n    return el;\n  };\n};\n"]}