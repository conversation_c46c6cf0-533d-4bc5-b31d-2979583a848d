{"version": 3, "file": "directive.js", "sourceRoot": "", "sources": ["src/directive.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAC,SAAS,IAAI,eAAe,EAAC,MAAM,oBAAoB,CAAC;AAChE,OAAO,KAAK,SAAS,MAAM,eAAe,CAAC;AAE3C,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,SAAS,EAAE,CAAC;IACZ,KAAK,EAAE,CAAC;IACR,QAAQ,EAAE,CAAC;IACX,iBAAiB,EAAE,CAAC;IACpB,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;CACF,CAAE;AAqBZ,MAAM,SAAS;IAIb,YAAY,UAA8B;QAHjC,SAAI,GAAG,QAAQ,CAAC,KAAK,CAAC;QAI7B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAW,CAAC;IAC/C,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;IACnC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;CACF;AAGD,MAAM,aAAa;IA4BjB,YAAY,UAA0D;QACpE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,YAAY,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;YACxD,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,QAAQ,CAAC,SAAS,CAAC;IACzB,CAAC;IA7BD,IAAI,OAAO;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC;IACxC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC;IAC3C,CAAC;IACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,CAAC;CAQF;AAGD,MAAM,oBAAoB;IA4BxB,YAAY,UAA0C;QA3B7C,SAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC;QA4BzC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IA1BD,IAAI,OAAO;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;IACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,CAAC;CAKF;AAcD,MAAM,SAAS;IAIb,YAAY,UAA+B;QAHlC,SAAI,GAAG,QAAQ,CAAC,KAAK,CAAC;QAI7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;IACnC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;CACF;AAED,sCAAsC;AAEtC,SAAS,gBAAgB,CAAC,IAAoB;IAC5C,IAAI,IAAI,YAAY,SAAS,CAAC,QAAQ,EAAE;QACtC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;KAC5B;SAAM,IAAI,IAAI,YAAY,SAAS,CAAC,SAAS,EAAE;QAC9C,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;KAC5B;SAAM,IAAI,IAAI,YAAY,SAAS,CAAC,oBAAoB,EAAE;QACzD,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;KACvC;SAAM,IACH,IAAI,YAAY,SAAS,CAAC,YAAY;QACtC,IAAI,YAAY,SAAS,CAAC,aAAa,EAAE;QAC3C,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;KAChC;IACD,+CAA+C;IAC/C,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;AACvC,CAAC;AA+BD;;;;GAIG;AACH,MAAM,OAAgB,SAAS;IAC7B,gEAAgE;IAChE,YAAY,SAAmB;IAC/B,CAAC;IAED,MAAM,CAAC,KAAW,EAAE,IAAoB;QACtC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;CACF;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,SAAS,CAA2B,cAAiB;IACnE,MAAM,cAAc,GAChB,IAAI,OAAO,EAAmD,CAAC;IACnE,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,IAAe,EAAE,EAAE;QACpD,OAAO,CAAC,IAAoB,EAAE,EAAE;YAC9B,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,UAAU,EAAE,QAAQ,CAAC;YACzB,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACpC,QAAQ,GAAG,IAAI,cAAc,CAAC,UAAU,CAAoB,CAAC;gBAC7D,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAU,CAAE,CAAC;aAC5D;iBAAM;gBACL,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvB,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;aACtB;YACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,MACkC,CAAC;AAC5C,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2021 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\nimport {directive as legacyDirective} from './lib/directive.js';\nimport * as legacyLit from './lit-html.js';\n\nexport const PartType = {\n  ATTRIBUTE: 1,\n  CHILD: 2,\n  PROPERTY: 3,\n  BOOLEAN_ATTRIBUTE: 4,\n  EVENT: 5,\n  ELEMENT: 6,\n} as const ;\n\nexport type PartType = typeof PartType[keyof typeof PartType];\n\nexport interface ChildPartInfo {\n  readonly type: typeof PartType.CHILD;\n}\n\nexport interface AttributePartInfo {\n  // eslint-disable-next-line @typescript-eslint/type-annotation-spacing\n  readonly type:|typeof PartType.ATTRIBUTE|\n      typeof PartType.PROPERTY|typeof PartType.BOOLEAN_ATTRIBUTE|\n      typeof PartType.EVENT;\n  readonly strings?: ReadonlyArray<string>;\n  readonly name: string;\n  readonly tagName: string;\n}\n\nexport type Part = ChildPart|AttributePart|BooleanAttributePart|EventPart;\n\nexport type{ChildPart};\nclass ChildPart {\n  readonly type = PartType.CHILD;\n  readonly options: legacyLit.RenderOptions|undefined;\n  readonly legacyPart: legacyLit.NodePart;\n  constructor(legacyPart: legacyLit.NodePart) {\n    this.options = legacyPart.options;\n    this.legacyPart = legacyPart;\n  }\n\n  get parentNode(): Node {\n    return this.legacyPart.startNode.parentNode!;\n  }\n\n  get startNode(): Node|null {\n    return this.legacyPart.startNode;\n  }\n\n  get endNode(): Node|null {\n    return this.legacyPart.endNode;\n  }\n}\n\nexport type{AttributePart};\nclass AttributePart {\n  readonly type: typeof PartType.ATTRIBUTE|typeof PartType.PROPERTY;\n  readonly legacyPart: legacyLit.AttributePart|legacyLit.PropertyPart;\n\n  get options(): legacyLit.RenderOptions|undefined {\n    return undefined;\n  }\n\n  get name(): string {\n    return this.legacyPart.committer.name;\n  }\n\n  get element(): Element {\n    return this.legacyPart.committer.element;\n  }\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  get strings() {\n    return this.legacyPart.committer.strings;\n  }\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  constructor(legacyPart: legacyLit.AttributePart|legacyLit.PropertyPart) {\n    this.legacyPart = legacyPart;\n    this.type = (legacyPart instanceof legacyLit.PropertyPart) ?\n        PartType.PROPERTY :\n        PartType.ATTRIBUTE;\n  }\n}\n\nexport type{BooleanAttributePart};\nclass BooleanAttributePart {\n  readonly type = PartType.BOOLEAN_ATTRIBUTE;\n  readonly legacyPart: legacyLit.BooleanAttributePart\n\n  get options(): legacyLit.RenderOptions|undefined {\n    return undefined;\n  }\n\n  get name(): string {\n    return this.legacyPart.name;\n  }\n\n  get element(): Element {\n    return this.legacyPart.element;\n  }\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  get strings() {\n    return this.legacyPart.strings;\n  }\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  constructor(legacyPart: legacyLit.BooleanAttributePart) {\n    this.legacyPart = legacyPart;\n  }\n}\n\n/**\n * An AttributePart that manages an event listener via add/removeEventListener.\n *\n * This part works by adding itself as the event listener on an element, then\n * delegating to the value passed to it. This reduces the number of calls to\n * add/removeEventListener if the listener changes frequently, such as when an\n * inline function is used as a listener.\n *\n * Because event options are passed when adding listeners, we must take care\n * to add and remove the part as a listener when the event options change.\n */\nexport type{EventPart};\nclass EventPart {\n  readonly type = PartType.EVENT;\n  readonly legacyPart: legacyLit.EventPart;\n\n  constructor(legacyPart: legacyLit.EventPart) {\n    this.legacyPart = legacyPart;\n  }\n\n  get options(): legacyLit.RenderOptions|undefined {\n    return undefined;\n  }\n\n  get name(): string {\n    return this.legacyPart.eventName;\n  }\n\n  get element(): Element {\n    return this.legacyPart.element;\n  }\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  get strings() {\n    return undefined;\n  }\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  handleEvent(event: Event) {\n    this.legacyPart.handleEvent(event);\n  }\n}\n\n// no equivalent for ElementPart in v1\n\nfunction legacyPartToPart(part: legacyLit.Part): Part {\n  if (part instanceof legacyLit.NodePart) {\n    return new ChildPart(part);\n  } else if (part instanceof legacyLit.EventPart) {\n    return new EventPart(part);\n  } else if (part instanceof legacyLit.BooleanAttributePart) {\n    return new BooleanAttributePart(part);\n  } else if (\n      part instanceof legacyLit.PropertyPart ||\n      part instanceof legacyLit.AttributePart) {\n    return new AttributePart(part);\n  }\n  // ElementPartInfo doesn't exist in lit-html v1\n  throw new Error(`Unknown part type`);\n}\n\n/**\n * Information about the part a directive is bound to.\n *\n * This is useful for checking that a directive is attached to a valid part,\n * such as with directive that can only be used on attribute bindings.\n */\nexport type PartInfo = ChildPartInfo|AttributePartInfo;\n\nexport interface DirectiveClass {\n  new(part: PartInfo): Directive;\n}\n\n/**\n * This utility type extracts the signature of a directive class's render()\n * method so we can use it for the type of the generated directive function.\n */\nexport type DirectiveParameters<C extends Directive> = Parameters<C['render']>;\n\n/**\n * A generated directive function doesn't evaluate the directive, but just\n * returns a DirectiveResult object that captures the arguments.\n */\nexport type DirectiveResult<C extends DirectiveClass = DirectiveClass> = {\n  /** @internal */\n  _$litDirective$: C;\n  /** @internal */\n  values: DirectiveParameters<InstanceType<C>>;\n};\n\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport abstract class Directive {\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  constructor(_partInfo: PartInfo) {\n  }\n  abstract render(...args: Array<unknown>): unknown;\n  update(_part: Part, args: Array<unknown>): unknown {\n    return this.render(...args);\n  }\n}\n\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n *\n * N.B. In Lit 2, the directive will lose state if another directive is\n * executed on the same part as the directive instance is destroyed. This\n * version deviates from this behavior and will keep its state.\n */\nexport function directive<C extends DirectiveClass>(directiveClass: C) {\n  const partToInstance =\n      new WeakMap<legacyLit.Part, readonly[Part, InstanceType<C>]>();\n  const result = legacyDirective((...args: unknown[]) => {\n    return (part: legacyLit.Part) => {\n      const cached = partToInstance.get(part);\n      let modernPart, instance;\n      if (cached === undefined) {\n        modernPart = legacyPartToPart(part);\n        instance = new directiveClass(modernPart) as InstanceType<C>;\n        partToInstance.set(part, [modernPart, instance] as const );\n      } else {\n        modernPart = cached[0];\n        instance = cached[1];\n      }\n      part.setValue(instance.update(modernPart, args));\n      part.commit();\n    };\n  });\n\n  return result as (...args: DirectiveParameters<InstanceType<C>>) =>\n             (part: legacyLit.Part) => void;\n}\n"]}