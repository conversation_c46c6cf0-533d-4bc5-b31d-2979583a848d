{"name": "lit-html", "version": "1.4.1", "description": "HTML template literals in JavaScript", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://lit-html.polymer-project.org/", "repository": "Polymer/lit-html", "type": "module", "main": "lit-html.js", "module": "lit-html.js", "typings": "lit-html.d.ts", "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "directories": {"test": "test"}, "files": ["/lit-html.js", "/lit-html.js.map", "/lit-html.d.ts", "/lit-html.d.ts.map", "/directive-helpers.js", "/directive-helpers.js.map", "/directive-helpers.d.ts", "/directive-helpers.d.ts.map", "/async-directive.js", "/async-directive.js.map", "/async-directive.d.ts", "/async-directive.d.ts.map", "/directive.js", "/directive.js.map", "/directive.d.ts", "/directive.d.ts.map", "/lib/", "/directives/", "/polyfills", "/src/", "/ts3.4/", "!/src/test/"], "scripts": {"build": "tsc && rm -rf ./ts3.4 && downlevel-dts . ts3.4 && cp tsconfig.json ./ts3.4/", "checksize": "rollup -c ; cat lit-html.bundled.js | gzip -9 | wc -c ; rm lit-html.bundled.js", "test": "npm run build && npm run lint", "quicktest": "wct -l chrome -p --npm", "format": "clang-format --version; find src test | grep '\\.js$\\|\\.ts$' | xargs clang-format --style=file -i", "lint": "npm run lint:eslint", "lint:eslint": "eslint 'src/**/*.{js,ts}'", "prepublishOnly": "node check-version-tracker.cjs && npm run lint && npm test", "prepare": "npm run build", "publish-dev": "npm test && VERSION=${npm_package_version%-*}-dev.`git rev-parse --short HEAD` && npm version --no-git-tag-version $VERSION && npm publish --tag dev"}, "author": "The Polymer Authors", "devDependencies": {"@types/chai": "^4.1.0", "@types/mocha": "^7.0.1", "@types/trusted-types": "^1.0.1", "@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "@webcomponents/shadycss": "^1.8.0", "@webcomponents/webcomponentsjs": "^2.4.2", "chai": "^4.1.2", "chromedriver": "^84.0.1", "clang-format": "~1.2.4", "downlevel-dts": "^0.4.0", "eslint": "^6.8.0", "husky": "^4.2.0", "lint-staged": "^10.1.0", "lit-html-benchmarks": "^0.2.1", "mocha": "^7.0.1", "rollup": "^1.19.0", "rollup-plugin-filesize": "^6.2.0", "rollup-plugin-terser": "^5.2.0", "tachometer": "^0.5.0", "typescript": "~3.8.0", "uglify-es": "^3.3.5", "wct-mocha": "^1.1.0", "web-component-tester": "^6.9.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,ts}": ["eslint --fix"]}}