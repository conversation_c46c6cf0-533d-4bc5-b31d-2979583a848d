{"version": 3, "file": "directive.js", "sourceRoot": "", "sources": ["../src/lib/directive.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAIH,MAAM,UAAU,GAAG,IAAI,OAAO,EAAgB,CAAC;AAO/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAA6B,CAAI,EAAK,EAAE,CAC7D,CAAC,CAAC,GAAG,IAAe,EAAE,EAAE;IACtB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACxB,OAAO,CAAC,CAAC;AACX,CAAC,CAAM,CAAC;AAEZ,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,CAAU,EAAoB,EAAE;IAC1D,OAAO,OAAO,CAAC,KAAK,UAAU,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\nimport {Part} from './part.js';\n\nconst directives = new WeakMap<object, true>();\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type DirectiveFactory = (...args: any[]) => object;\n\nexport type DirectiveFn = (part: Part) => void;\n\n/**\n * Brands a function as a directive factory function so that lit-html will call\n * the function during template rendering, rather than passing as a value.\n *\n * A _directive_ is a function that takes a Part as an argument. It has the\n * signature: `(part: Part) => void`.\n *\n * A directive _factory_ is a function that takes arguments for data and\n * configuration and returns a directive. Users of directive usually refer to\n * the directive factory as the directive. For example, \"The repeat directive\".\n *\n * Usually a template author will invoke a directive factory in their template\n * with relevant arguments, which will then return a directive function.\n *\n * Here's an example of using the `repeat()` directive factory that takes an\n * array and a function to render an item:\n *\n * ```js\n * html`<ul><${repeat(items, (item) => html`<li>${item}</li>`)}</ul>`\n * ```\n *\n * When `repeat` is invoked, it returns a directive function that closes over\n * `items` and the template function. When the outer template is rendered, the\n * return directive function is called with the Part for the expression.\n * `repeat` then performs it's custom logic to render multiple items.\n *\n * @param f The directive factory function. Must be a function that returns a\n * function of the signature `(part: Part) => void`. The returned function will\n * be called with the part object.\n *\n * @example\n *\n * import {directive, html} from 'lit-html';\n *\n * const immutable = directive((v) => (part) => {\n *   if (part.value !== v) {\n *     part.setValue(v)\n *   }\n * });\n */\nexport const directive = <F extends DirectiveFactory>(f: F): F =>\n    ((...args: unknown[]) => {\n      const d = f(...args);\n      directives.set(d, true);\n      return d;\n    }) as F;\n\nexport const isDirective = (o: unknown): o is DirectiveFn => {\n  return typeof o === 'function' && directives.has(o);\n};\n"]}