{"version": 3, "file": "default-template-processor.js", "sourceRoot": "", "sources": ["../src/lib/default-template-processor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAGH,OAAO,EAAC,kBAAkB,EAAE,oBAAoB,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAC,MAAM,YAAY,CAAC;AAI5G;;GAEG;AACH,MAAM,OAAO,wBAAwB;IACnC;;;;;;;;OAQG;IACH,0BAA0B,CACtB,OAAgB,EAAE,IAAY,EAAE,OAAiB,EACjD,OAAsB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,MAAM,SAAS,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACzE,OAAO,SAAS,CAAC,KAAK,CAAC;SACxB;QACD,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;SACtE;QACD,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,CAAC,IAAI,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;SACpE;QACD,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACjE,OAAO,SAAS,CAAC,KAAK,CAAC;IACzB,CAAC;IACD;;;OAGG;IACH,oBAAoB,CAAC,OAAsB;QACzC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AAED,MAAM,CAAC,MAAM,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\nimport {Part} from './part.js';\nimport {AttributeCommitter, BooleanAttributePart, EventPart, NodePart, PropertyCommitter} from './parts.js';\nimport {RenderOptions} from './render-options.js';\nimport {TemplateProcessor} from './template-processor.js';\n\n/**\n * Creates Parts when a template is instantiated.\n */\nexport class DefaultTemplateProcessor implements TemplateProcessor {\n  /**\n   * Create parts for an attribute-position binding, given the event, attribute\n   * name, and string literals.\n   *\n   * @param element The element containing the binding\n   * @param name  The attribute name\n   * @param strings The string literals. There are always at least two strings,\n   *   event for fully-controlled bindings with a single expression.\n   */\n  handleAttributeExpressions(\n      element: Element, name: string, strings: string[],\n      options: RenderOptions): ReadonlyArray<Part> {\n    const prefix = name[0];\n    if (prefix === '.') {\n      const committer = new PropertyCommitter(element, name.slice(1), strings);\n      return committer.parts;\n    }\n    if (prefix === '@') {\n      return [new EventPart(element, name.slice(1), options.eventContext)];\n    }\n    if (prefix === '?') {\n      return [new BooleanAttributePart(element, name.slice(1), strings)];\n    }\n    const committer = new AttributeCommitter(element, name, strings);\n    return committer.parts;\n  }\n  /**\n   * Create parts for a text-position binding.\n   * @param templateFactory\n   */\n  handleTextExpression(options: RenderOptions) {\n    return new NodePart(options);\n  }\n}\n\nexport const defaultTemplateProcessor = new DefaultTemplateProcessor();\n"]}