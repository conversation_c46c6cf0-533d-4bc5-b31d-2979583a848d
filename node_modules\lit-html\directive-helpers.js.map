{"version": 3, "file": "directive-helpers.js", "sourceRoot": "", "sources": ["src/directive-helpers.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAC,cAAc,EAAC,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAC,WAAW,EAAC,MAAM,gBAAgB,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,KAAc,EAA2B,EAAE,CACxE,KAAK,YAAY,cAAc,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2021 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\nimport {TemplateResult} from './lit-html.js';\nexport {isPrimitive} from './lib/parts.js';\n\n/**\n * Tests if a value is a TemplateResult.\n */\nexport const isTemplateResult = (value: unknown): value is TemplateResult =>\n    value instanceof TemplateResult;\n"]}