{"version": 3, "file": "directive.d.ts", "sourceRoot": "", "sources": ["src/directive.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAGH,OAAO,KAAK,SAAS,MAAM,eAAe,CAAC;AAE3C,eAAO,MAAM,QAAQ;;;;;;;CAOX,CAAE;AAEZ,oBAAY,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,CAAC;AAE9D,MAAM,WAAW,aAAa;IAC5B,QAAQ,CAAC,IAAI,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC;CACtC;AAED,MAAM,WAAW,iBAAiB;IAEhC,QAAQ,CAAC,IAAI,EAAE,OAAO,QAAQ,CAAC,SAAS,GACpC,OAAO,QAAQ,CAAC,QAAQ,GAAC,OAAO,QAAQ,CAAC,iBAAiB,GAC1D,OAAO,QAAQ,CAAC,KAAK,CAAC;IAC1B,QAAQ,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IACzC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;CAC1B;AAED,oBAAY,IAAI,GAAG,SAAS,GAAC,aAAa,GAAC,oBAAoB,GAAC,SAAS,CAAC;AAE1E,YAAW,EAAC,SAAS,EAAC,CAAC;AACvB,cAAM,SAAS;IACb,QAAQ,CAAC,IAAI,IAAkB;IAC/B,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,aAAa,GAAC,SAAS,CAAC;IACpD,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC;gBAC5B,UAAU,EAAE,SAAS,CAAC,QAAQ;IAK1C,IAAI,UAAU,IAAI,IAAI,CAErB;IAED,IAAI,SAAS,IAAI,IAAI,GAAC,IAAI,CAEzB;IAED,IAAI,OAAO,IAAI,IAAI,GAAC,IAAI,CAEvB;CACF;AAED,YAAW,EAAC,aAAa,EAAC,CAAC;AAC3B,cAAM,aAAa;IACjB,QAAQ,CAAC,IAAI,EAAE,OAAO,QAAQ,CAAC,SAAS,GAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC;IAClE,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,aAAa,GAAC,SAAS,CAAC,YAAY,CAAC;IAEpE,IAAI,OAAO,IAAI,SAAS,CAAC,aAAa,GAAC,SAAS,CAE/C;IAED,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;;;OAIG;IACH,IAAI,OAAO,sBAEV;IACD,IAAI,OAAO,WAEV;gBAEW,UAAU,EAAE,SAAS,CAAC,aAAa,GAAC,SAAS,CAAC,YAAY;CAMvE;AAED,YAAW,EAAC,oBAAoB,EAAC,CAAC;AAClC,cAAM,oBAAoB;IACxB,QAAQ,CAAC,IAAI,IAA8B;IAC3C,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,oBAAoB,CAAA;IAEnD,IAAI,OAAO,IAAI,SAAS,CAAC,aAAa,GAAC,SAAS,CAE/C;IAED,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;;;OAIG;IACH,IAAI,OAAO,sBAEV;IACD,IAAI,OAAO,WAEV;gBAEW,UAAU,EAAE,SAAS,CAAC,oBAAoB;CAGvD;AAED;;;;;;;;;;GAUG;AACH,YAAW,EAAC,SAAS,EAAC,CAAC;AACvB,cAAM,SAAS;IACb,QAAQ,CAAC,IAAI,IAAkB;IAC/B,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,SAAS,CAAC;gBAE7B,UAAU,EAAE,SAAS,CAAC,SAAS;IAI3C,IAAI,OAAO,IAAI,SAAS,CAAC,aAAa,GAAC,SAAS,CAE/C;IAED,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;;;OAIG;IACH,IAAI,OAAO,cAEV;IACD,IAAI,OAAO,WAEV;IAED,WAAW,CAAC,KAAK,EAAE,KAAK;CAGzB;AAoBD;;;;;GAKG;AACH,oBAAY,QAAQ,GAAG,aAAa,GAAC,iBAAiB,CAAC;AAEvD,MAAM,WAAW,cAAc;IAC7B,KAAI,IAAI,EAAE,QAAQ,GAAG,SAAS,CAAC;CAChC;AAED;;;GAGG;AACH,oBAAY,mBAAmB,CAAC,CAAC,SAAS,SAAS,IAAI,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE/E;;;GAGG;AACH,oBAAY,eAAe,CAAC,CAAC,SAAS,cAAc,GAAG,cAAc,IAAI;IACvE,gBAAgB;IAChB,eAAe,EAAE,CAAC,CAAC;IACnB,gBAAgB;IAChB,MAAM,EAAE,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9C,CAAC;AAEF;;;;GAIG;AACH,8BAAsB,SAAS;gBAEjB,SAAS,EAAE,QAAQ;IAE/B,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;IACjD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;CAGnD;AAED;;;;;;;GAOG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,cAAc,EAAE,cAAc,EAAE,CAAC,sFAsBpE"}