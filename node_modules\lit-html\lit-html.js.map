{"version": 3, "file": "lit-html.js", "sourceRoot": "", "sources": ["src/lit-html.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH;;;;;;;;;;;GAWG;AAEH;;;GAGG;AACH,OAAO,EAAC,wBAAwB,EAAC,MAAM,qCAAqC,CAAC;AAC7E,OAAO,EAAC,iBAAiB,EAAE,cAAc,EAAC,MAAM,0BAA0B,CAAC;AAE3E,OAAO,EAAC,wBAAwB,EAAE,wBAAwB,EAAC,MAAM,qCAAqC,CAAC;AACvG,OAAO,EAAC,SAAS,EAAe,WAAW,EAAC,MAAM,oBAAoB,CAAC;AACvE,uEAAuE;AACvE,OAAO,EAAC,WAAW,EAAE,aAAa,EAAC,MAAM,cAAc,CAAC;AACxD,OAAO,EAAC,QAAQ,EAAE,OAAO,EAAO,MAAM,eAAe,CAAC;AACtD,OAAO,EAAC,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAE,YAAY,EAAC,MAAM,gBAAgB,CAAC;AAEtK,OAAO,EAAC,KAAK,EAAE,MAAM,EAAC,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAC,cAAc,EAAE,eAAe,EAAC,MAAM,2BAA2B,CAAC;AAC1E,OAAO,EAAC,gBAAgB,EAAC,MAAM,4BAA4B,CAAC;AAE5D,OAAO,EAAC,iBAAiB,EAAE,cAAc,EAAC,MAAM,0BAA0B,CAAC;AAC3E,OAAO,EAAC,YAAY,EAAE,oBAAoB,EAAE,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAQ/E,2EAA2E;AAC3E,kEAAkE;AAClE,2DAA2D;AAC3D,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IACjC,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CAC/E;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,OAA6B,EAAE,GAAG,MAAiB,EAAE,EAAE,CACxE,IAAI,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,wBAAwB,CAAC,CAAC;AAE1E;;;GAGG;AACH,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,OAA6B,EAAE,GAAG,MAAiB,EAAE,EAAE,CACvE,IAAI,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,wBAAwB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2017 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\n/**\n *\n * Main lit-html module.\n *\n * Main exports:\n *\n * -  [[html]]\n * -  [[svg]]\n * -  [[render]]\n *\n * @packageDocumentation\n */\n\n/**\n * Do not remove this comment; it keeps typedoc from misplacing the module\n * docs.\n */\nimport {defaultTemplateProcessor} from './lib/default-template-processor.js';\nimport {SVGTemplateResult, TemplateResult} from './lib/template-result.js';\n\nexport {DefaultTemplateProcessor, defaultTemplateProcessor} from './lib/default-template-processor.js';\nexport {directive, DirectiveFn, isDirective} from './lib/directive.js';\n// TODO(justinfagnani): remove line when we get NodePart moving methods\nexport {removeNodes, reparentNodes} from './lib/dom.js';\nexport {noChange, nothing, Part} from './lib/part.js';\nexport {AttributeCommitter, AttributePart, BooleanAttributePart, EventPart, isIterable, isPrimitive, NodePart, PropertyCommitter, PropertyPart} from './lib/parts.js';\nexport {RenderOptions} from './lib/render-options.js';\nexport {parts, render} from './lib/render.js';\nexport {templateCaches, templateFactory} from './lib/template-factory.js';\nexport {TemplateInstance} from './lib/template-instance.js';\nexport {TemplateProcessor} from './lib/template-processor.js';\nexport {SVGTemplateResult, TemplateResult} from './lib/template-result.js';\nexport {createMarker, isTemplatePartActive, Template} from './lib/template.js';\n\ndeclare global {\n  interface Window {\n    litHtmlVersions: string[];\n  }\n}\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n// TODO(justinfagnani): inject version number at build time\nif (typeof window !== 'undefined') {\n  (window['litHtmlVersions'] || (window['litHtmlVersions'] = [])).push('1.4.1');\n}\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n */\nexport const html = (strings: TemplateStringsArray, ...values: unknown[]) =>\n    new TemplateResult(strings, values, 'html', defaultTemplateProcessor);\n\n/**\n * Interprets a template literal as an SVG template that can efficiently\n * render to and update a container.\n */\nexport const svg = (strings: TemplateStringsArray, ...values: unknown[]) =>\n    new SVGTemplateResult(strings, values, 'svg', defaultTemplateProcessor);\n"]}