{"version": 3, "file": "async-directive.js", "sourceRoot": "", "sources": ["src/async-directive.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAC,SAAS,EAAiB,MAAM,gBAAgB,CAAC;AACzD,OAAO,EAAC,aAAa,IAAI,mBAAmB,EAAE,oBAAoB,IAAI,0BAA0B,EAAE,SAAS,IAAI,eAAe,EAAE,QAAQ,IAAI,cAAc,EAAsB,YAAY,IAAI,kBAAkB,GAAE,MAAM,eAAe,CAAC;AAE1O;;GAEG;AACH,MAAM,OAAgB,cAAe,SAAQ,SAAS;IAIpD,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAHV,iBAAY,GAAG,KAAK,CAAC;QAI3B,IAAI,CAAC,WAAW,GAAI,QAAiB,CAAC,UAAU,CAAC;IACnD,CAAC;IAEO,cAAc;QACpB,IAAI,IAAI,CAAC,WAAW,YAAY,cAAc,EAAE;YAC9C,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;SACnC;aAAM,IAAI,IAAI,CAAC,WAAW,YAAY,eAAe,EAAE;YACtD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;SACjC;aAAM,IAAI,IAAI,CAAC,WAAW,YAAY,0BAA0B,EAAE;YACjE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;SACjC;aAAM,IACH,IAAI,CAAC,WAAW,YAAY,kBAAkB;YAC9C,IAAI,CAAC,WAAW,YAAY,mBAAmB,EAAE;YACnD,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;SAC3C;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,OAAO,IAAI,CAAC;SACb;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACnC,OAAO,CAAC,CAAC,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC;IAC/B,CAAC;IAED,QAAQ,CAAC,KAAc;QACrB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;YACzB,oCAAoC;YACpC,OAAO;SACR;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;;;;;;;;;OAUG;IACH,gEAAgE;IACtD,YAAY;IACtB,CAAC;IACD;;;;;;;;OAQG;IACH,gEAAgE;IACtD,WAAW;IACrB,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright (c) 2021 The Polymer Project Authors. All rights reserved.\n * This code may only be used under the BSD style license found at\n * http://polymer.github.io/LICENSE.txt\n * The complete set of authors may be found at\n * http://polymer.github.io/AUTHORS.txt\n * The complete set of contributors may be found at\n * http://polymer.github.io/CONTRIBUTORS.txt\n * Code distributed by Google as part of the polymer project is also\n * subject to an additional IP rights grant found at\n * http://polymer.github.io/PATENTS.txt\n */\n\nimport {Directive, Part, PartInfo} from './directive.js';\nimport {AttributePart as LegacyAttributePart, BooleanAttributePart as LegacyBooleanAttributePart, EventPart as LegacyEventPart, NodePart as LegacyNodePart, Part as LegacyPart, PropertyPart as LegacyPropertyPart,} from './lit-html.js';\n\n/**\n * A superclass for directives that need to asynchronously update.\n */\nexport abstract class AsyncDirective extends Directive {\n  private readonly _legacyPart: LegacyPart;\n  private _renderedYet = false;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    this._legacyPart = (partInfo as Part).legacyPart;\n  }\n\n  private _legacyGetNode(): Node|undefined {\n    if (this._legacyPart instanceof LegacyNodePart) {\n      return this._legacyPart.startNode;\n    } else if (this._legacyPart instanceof LegacyEventPart) {\n      return this._legacyPart.element;\n    } else if (this._legacyPart instanceof LegacyBooleanAttributePart) {\n      return this._legacyPart.element;\n    } else if (\n        this._legacyPart instanceof LegacyPropertyPart ||\n        this._legacyPart instanceof LegacyAttributePart) {\n      return this._legacyPart.committer.element;\n    }\n    return undefined;\n  }\n\n  private _shouldRender() {\n    if (!this._renderedYet) {\n      this._renderedYet = true;\n      return true;\n    }\n    const node = this._legacyGetNode();\n    return !!(node?.isConnected);\n  }\n\n  setValue(value: unknown) {\n    if (!this._shouldRender()) {\n      // node is disconnected, do nothing.\n      return;\n    }\n\n    this._legacyPart.setValue(value);\n    this._legacyPart.commit();\n  }\n\n  /**\n   * User callback for implementing logic to release any\n   * resources/subscriptions that may have been retained by this directive.\n   * Since directives may also be re-connected, `reconnected` should also be\n   * implemented to restore the working state of the directive prior to the next\n   * render.\n   *\n   * NOTE: In lit-html 1.x, the `disconnected` and `reconnected` callbacks WILL\n   * NOT BE CALLED. The interface is provided here for forward-compatible\n   * directive authoring only.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  protected disconnected() {\n  }\n  /**\n   * User callback to restore the working state of the directive prior to the\n   * next render. This should generally re-do the work that was undone in\n   * `disconnected`.\n   *\n   * NOTE: In lit-html 1.x, the `disconnected` and `reconnected` callbacks WILL\n   * NOT BE CALLED. The interface is provided here for forward-compatible\n   * directive authoring only.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  protected reconnected() {\n  }\n}\n"]}